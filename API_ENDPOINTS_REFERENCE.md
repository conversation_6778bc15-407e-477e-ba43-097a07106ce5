# Complete API Endpoints Reference for Next.js Staff Website

## 🔐 Authentication APIs

### Base URL: `/api/auth/`

| Method | Endpoint | Description | Auth Required | User Type |
|--------|----------|-------------|---------------|-----------|
| POST | `/login/email/` | Staff login with email/password | No | Staff |
| POST | `/logout/` | Logout user | Yes | All |
| GET | `/profile/` | Get current user profile | Yes | All |
| PUT | `/profile/` | Update user profile | Yes | All |
| GET | `/users/` | List all users with filters | Yes | Staff |
| GET | `/users/{id}/` | Get specific user details | Yes | Staff |
| PUT | `/users/{id}/` | Update user details | Yes | Staff |
| POST | `/users/{id}/unlock/` | Unlock user account | Yes | Staff |
| GET | `/admin/user-stats/` | Get user statistics | Yes | Staff |
| GET | `/addresses/` | List user addresses | Yes | All |
| POST | `/addresses/` | Create new address | Yes | All |
| PUT | `/addresses/{id}/` | Update address | Yes | All |
| DELETE | `/addresses/{id}/` | Delete address | Yes | All |

### Request/Response Examples

#### Staff Login
```http
POST /api/auth/login/email/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

Response:
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "Staff User",
    "user_type": "STAFF",
    "is_staff": true
  }
}
```

## 🏪 Catalogue APIs

### Base URL: `/api/catalogue/`

| Method | Endpoint | Description | Auth Required | User Type |
|--------|----------|-------------|---------------|-----------|
| GET | `/categories/` | List all categories | No | All |
| POST | `/categories/` | Create new category | Yes | Staff |
| GET | `/categories/tree/` | Get category tree structure | No | All |
| GET | `/categories/{slug}/` | Get category details | No | All |
| PUT | `/categories/{slug}/` | Update category | Yes | Staff |
| DELETE | `/categories/{slug}/` | Delete category | Yes | Staff |
| GET | `/services/` | List all services with filters | No | All |
| POST | `/services/` | Create new service | Yes | Staff |
| GET | `/services/{id}/` | Get service details | No | All |
| PUT | `/services/{id}/` | Update service | Yes | Staff |
| DELETE | `/services/{id}/` | Delete service | Yes | Staff |
| GET | `/services/search/` | Search services | No | All |

### Query Parameters for Services
```
?category=1&is_active=true&search=cleaning&page=1&ordering=-created_at
```

## 🛒 Cart APIs

### Base URL: `/api/cart/`

| Method | Endpoint | Description | Auth Required | User Type |
|--------|----------|-------------|---------------|-----------|
| GET | `/` | Get current cart details | Yes | Customer |
| GET | `/summary/` | Get cart summary | Yes | Customer |
| POST | `/add/` | Add item to cart | Yes | Customer |
| PUT | `/items/{id}/update/` | Update cart item quantity | Yes | Customer |
| DELETE | `/items/{id}/remove/` | Remove item from cart | Yes | Customer |
| DELETE | `/clear/` | Clear entire cart | Yes | Customer |
| POST | `/coupon/apply/` | Apply coupon to cart | Yes | Customer |
| DELETE | `/coupon/remove/` | Remove coupon from cart | Yes | Customer |
| GET | `/admin/carts/` | List all carts (admin) | Yes | Staff |
| GET | `/admin/cart-items/` | List all cart items (admin) | Yes | Staff |

## 🎫 Coupon APIs

### Base URL: `/api/coupons/`

| Method | Endpoint | Description | Auth Required | User Type |
|--------|----------|-------------|---------------|-----------|
| GET | `/` | List all coupons | Yes | Staff |
| POST | `/` | Create new coupon | Yes | Staff |
| GET | `/{code}/` | Get coupon details | No | All |
| PUT | `/{code}/` | Update coupon | Yes | Staff |
| DELETE | `/{code}/` | Delete coupon | Yes | Staff |
| POST | `/validate/` | Validate coupon code | Yes | Customer |
| POST | `/apply/` | Apply coupon to cart | Yes | Customer |
| GET | `/usage/` | Get coupon usage statistics | Yes | Staff |
| GET | `/admin/coupons/` | Admin coupon list with stats | Yes | Staff |
| GET | `/admin/used-coupons/` | Coupon usage tracking | Yes | Staff |

## 📦 Order APIs

### Base URL: `/api/orders/`

| Method | Endpoint | Description | Auth Required | User Type |
|--------|----------|-------------|---------------|-----------|
| GET | `/` | List orders with filters | Yes | Staff/Customer |
| POST | `/` | Create new order | Yes | Customer |
| GET | `/dashboard/` | Order statistics dashboard | Yes | Staff |
| GET | `/{order_number}/` | Get order details | Yes | Staff/Customer |
| PUT | `/{order_number}/` | Update order | Yes | Staff |
| POST | `/{order_number}/update-status/` | Update order status | Yes | Staff |
| POST | `/{order_number}/assign-provider/` | Assign provider to order | Yes | Staff |
| POST | `/{order_number}/cancel/` | Cancel order | Yes | Staff/Customer |
| POST | `/cod/` | Create COD order | Yes | Customer |

### Order Filters
```
?status=pending&payment_status=paid&customer=123&date_from=2024-01-01&date_to=2024-12-31
```

## 💳 Payment APIs

### Base URL: `/api/payments/`

| Method | Endpoint | Description | Auth Required | User Type |
|--------|----------|-------------|---------------|-----------|
| GET | `/config/` | Get payment configuration | Yes | Staff |
| PUT | `/config/` | Update payment configuration | Yes | Staff |
| GET | `/transactions/` | List payment transactions | Yes | Staff |
| GET | `/transactions/{id}/` | Get transaction details | Yes | Staff |
| POST | `/razorpay/create-order/` | Create Razorpay order | Yes | Customer |
| POST | `/razorpay/verify-payment/` | Verify Razorpay payment | Yes | Customer |
| POST | `/cod/confirm/` | Confirm COD payment | Yes | Staff |
| POST | `/refund/` | Process refund | Yes | Staff |

## 👥 Provider APIs

### Base URL: `/api/providers/`

| Method | Endpoint | Description | Auth Required | User Type |
|--------|----------|-------------|---------------|-----------|
| GET | `/` | List all providers | Yes | Staff |
| POST | `/` | Create provider profile | Yes | Staff |
| GET | `/{id}/` | Get provider details | Yes | Staff |
| PUT | `/{id}/` | Update provider profile | Yes | Staff |
| POST | `/{id}/verify/` | Verify provider | Yes | Staff |
| POST | `/{id}/documents/` | Upload provider documents | Yes | Provider/Staff |
| GET | `/{id}/documents/` | List provider documents | Yes | Staff |
| PUT | `/{id}/bank-details/` | Update bank details | Yes | Provider/Staff |
| GET | `/{id}/availability/` | Get provider availability | Yes | Staff |
| POST | `/{id}/payout-request/` | Create payout request | Yes | Provider |

## 📅 Scheduling APIs

### Base URL: `/api/scheduling/`

| Method | Endpoint | Description | Auth Required | User Type |
|--------|----------|-------------|---------------|-----------|
| GET | `/configurations/` | List slot configurations | Yes | Staff |
| POST | `/configurations/` | Create slot configuration | Yes | Staff |
| GET | `/configurations/{id}/` | Get configuration details | Yes | Staff |
| PUT | `/configurations/{id}/` | Update configuration | Yes | Staff |
| GET | `/working-shifts/` | List working shifts | Yes | Staff |
| POST | `/working-shifts/` | Create working shift | Yes | Staff |
| GET | `/holidays/` | List holidays | Yes | Staff |
| POST | `/holidays/` | Create holiday | Yes | Staff |
| GET | `/slots/` | List time slots | Yes | Staff |
| GET | `/slots/available/` | Get available slots | Yes | Customer |
| POST | `/slots/generate/` | Generate time slots | Yes | Staff |
| PUT | `/slots/bulk-update/` | Bulk update slots | Yes | Staff |
| GET | `/bookings/` | List slot bookings | Yes | Staff |
| POST | `/bookings/create/` | Create slot booking | Yes | Customer |

## 🧮 Taxation APIs

### Base URL: `/api/taxation/`

| Method | Endpoint | Description | Auth Required | User Type |
|--------|----------|-------------|---------------|-----------|
| GET | `/admin/tax-categories/` | List tax categories | Yes | Staff |
| POST | `/admin/tax-categories/` | Create tax category | Yes | Staff |
| GET | `/admin/gst-rates/` | List GST rates | Yes | Staff |
| POST | `/admin/gst-rates/` | Create GST rate | Yes | Staff |
| GET | `/admin/tax-configurations/` | List tax configurations | Yes | Staff |
| POST | `/admin/tax-configurations/` | Create tax configuration | Yes | Staff |
| POST | `/calculate-preview/` | Preview tax calculation | Yes | Staff |
| POST | `/quick-setup/` | Quick tax setup | Yes | Staff |

## 🔧 Common Request Headers

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
Accept: application/json
```

## 📊 Common Response Format

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation successful"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field_name": ["This field is required"]
    }
  }
}
```

### Paginated Response
```json
{
  "count": 150,
  "next": "http://localhost:8000/api/orders/?page=2",
  "previous": null,
  "results": [ ... ]
}
```

## 🚀 Environment Variables for API Integration

```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_MEDIA_URL=http://localhost:8000/media/
```

This reference provides all the API endpoints needed to build a complete Next.js staff website that replicates Django admin functionality.
